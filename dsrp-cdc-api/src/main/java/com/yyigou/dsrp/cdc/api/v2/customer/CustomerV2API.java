package com.yyigou.dsrp.cdc.api.v2.customer;

import com.yyigou.ddc.common.page.PageDto;
import com.yyigou.ddc.common.page.PageVo;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBase;
import com.yyigou.ddc.services.ddc.task.message.incoming.DataImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.message.incoming.UapExcelImportDataSpiceMessage;
import com.yyigou.ddc.services.ddc.task.vo.incoming.ImportTaskDataSpiceVO;
import com.yyigou.ddc.services.ddc.uim.vo.OrganizationVo;
import com.yyigou.dsrp.cdc.api.v2.company.dto.CompanyLinkmanQueryDTO;
import com.yyigou.dsrp.cdc.api.v2.company.vo.CompanyLinkmanVO;
import com.yyigou.dsrp.cdc.api.v2.customer.dto.*;
import com.yyigou.dsrp.cdc.api.v2.customer.vo.*;

import java.util.List;

public interface CustomerV2API extends ServiceBase {

    /**
     * 管理视角：分页查询客户档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CustomerPageVO>> manageFindListPage(CustomerManageQueryListPageDTO params, PageDto pageParams);

    /**
     * 使用视角：分页查询客户档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CustomerPageVO>> useFindListPage(CustomerUseQueryListPageDTO params, PageDto pageParams);


    /**
     * 使用视角：分页查询客户档案列表
     *
     * @param params     查询条件
     * @param pageParams 分页条件
     * @return
     */
    CallResult<PageVo<CustomerReversePageVO>> reverseUseFindListPageNoAuthZ(CustomerUseQueryListPageNoAuthZDTO params, PageDto pageParams);


    /**
     * 管理视角：保存客户档案
     *
     * @param params
     * @return: {@link CallResult < Boolean>}
     */
//    CallResult<String> manageSaveCustomer(CustomerSaveDTO params);

    /**
     * 管理视角：删除客户档案
     *
     * @param params
     * @return: {@link CallResult < Boolean>}
     */
    CallResult<Boolean> manageDeleteCustomer(CustomerDeleteDTO params);



    /**
     * 共同视角：更新客户档案
     *
     * @param params
     * @return
     */
//    CallResult<Boolean> updateCustomer(CustomerUpdateDTO params);

    /**
     * 共同视角：获取客户档案详情
     *
     * @param params
     * @return
     */
    CallResult<CustomerVO> getCustomer(CustomerGetDTO params);

    /**
     * 共同视角：启停客户档案
     *
     * @param params
     * @return
     */
    CallResult<Boolean> changeCustomerStatus(CustomerChangeStatusDTO params);


    /**
     * 共同视角：查询客户档案分派
     *
     * @param params
     * @return
     */
    CallResult<List<CustomerAssignVO>> getAssignCustomer(CustomerGetAssignDTO params);

    /**
     * @description: 查询管理组织待处理客户数量
     * @author: baoww
     * @date: 17:29
     * @return: com.yyigou.ddc.common.service.CallResult<java.lang.Long>
     */
    CallResult<Long> getPendingCount();

    /**
     * 校验编码唯一性
     * @param no
     * @param code
     * @return
     */
    CallResult<Boolean> checkOnlyCode(String no, String code);

    /**
     * 校验名称唯一性
     * @param no
     * @param name
     * @return
     */
    CallResult<Boolean> checkOnlyName(String no, String name);

    CallResult<PageVo<CustomerPageVO>> queryPageCustomer(CustomerPageQueryDTO params, PageDto pageDto);

    CallResult<PageVo<CustomerPageVO>> querySpecifyOrgPageCustomer(CustomerPageQueryBySpecifyOrgDTO params, PageDto pageDto);

    CallResult<PageVo<CustomerFormalPageVO>> findListPageByFormal(CustomerPageByFormalQueryDTO params, PageDto pageDto);

    CallResult<PageVo<CustomerPageVO>> findNotInPageList(CustomerNotInPageListQueryDTO params, PageDto pageDto);


    CallResult<String> manageSaveCustomerBasicAndBiz(CustomerSaveBasicAndBizDTO params);

    CallResult<Boolean> editCustomerBasicAndBiz(CustomerEditBasicAndBizDTO params);

    CallResult<List<Long>> editCustomerAddressList(CustomerAddressListEditDTO params);

    CallResult<List<Long>> editCustomerLinkmanList(CustomerLinkmanListEditDTO params);

    CallResult<List<Long>> editCustomerSalesmanList(CustomerSalesmanListEditDTO params);

    CallResult<List<Long>> editCustomerInvoiceList(CustomerInvoiceListEditDTO params);

    CallResult<List<Long>> editCustomerBankList(CustomerBankListEditDTO params);


    CallResult<PageVo<OrganizationVo>> findOrgListPageBySetting(CustomerEligibleOrgListQueryDTO params, PageDto pageParams);

    /**
     * 分片校验导入的客户
     *
     * @param uapexcelImportDataSpiceMessage
     * @return
     */
    CallResult<ImportTaskDataSpiceVO> checkExcelSliceData(UapExcelImportDataSpiceMessage uapexcelImportDataSpiceMessage);


    /**
     * 分片导入客户
     *
     * @param dataImportDataSpiceMessage
     * @return
     */
    CallResult<ImportTaskDataSpiceVO> handleImportSliceData(DataImportDataSpiceMessage dataImportDataSpiceMessage);

    CallResult<PageVo<CustomerAddressVO>> findCustomerLinkAddressPage(CustomerAddressQueryDTO params, PageDto pageDto);

    CallResult<List<CustomerAddressVO>> saveCustomerLinkAddress(CustomerAddressListSaveDTO params);

    CallResult<Boolean> deleteCustomerLinkAddress(CustomerAddressListDeleteDTO params);

    CallResult<Boolean> updateCustomerLinkAddress(CustomerAddressListUpdateDTO params);

    CallResult<List<CustomerSalesManVO>> findCustomerSalesManList(CustomerSalesManQueryDTO params);

    CallResult<PageVo<CustomerSalesManVO>> findCustomerSalesManPage(CustomerSalesManQueryDTO params, PageDto pageDto);

    CallResult<List<CustomerInvoiceVO>> getInvoiceListByCustomerCode(CustomerInvoiceQueryDTO params);

    CallResult<List<CompanyLinkmanVO>> saveCustomerLinkman(CustomerLinkmanListSaveDTO params);

    CallResult<Boolean> updateCustomerLinkman(CustomerLinkmanListUpdateDTO params);

    CallResult<Boolean> deleteCustomerLinkman(CustomerLinkmanListDeleteDTO params);

    CallResult<PageVo<CompanyLinkmanVO>> findCustomerLinkmanListPage(CompanyLinkmanQueryDTO params, PageDto pageParams);

    CallResult<List<CompanyLinkmanVO>> findCustomerLinkmanList(CompanyLinkmanQueryDTO params);



    /**
     * 检测管理组织是否可转供应商
     *
     * @param params
     * @return
     */
    CallResult<Boolean> checkConvertToSupplier(CustomerConvertToSupplierDTO params);
}
