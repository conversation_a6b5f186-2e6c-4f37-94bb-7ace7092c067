package com.yyigou.dsrp.cdc.common.nc;

import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.zkmonitor.EnableZkMonitor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
@Slf4j
public class NcMapping {
    /**
     * ZK配置热更新
     */
    @EnableZkMonitor(zkProperty = "ncCustomerCategoryMapping", zkNode = "/ddc-config/service-dsrp-cdc")
    private volatile String ncCustomerCategoryMapping;

    @EnableZkMonitor(zkProperty = "ncCustomerHospitalClassMapping", zkNode = "/ddc-config/service-dsrp-cdc")
    private volatile String ncCustomerHospitalClassMapping;

    @EnableZkMonitor(zkProperty = "ncCustomerHospitalTypeMapping", zkNode = "/ddc-config/service-dsrp-cdc")
    private volatile String ncCustomerHospitalTypeMapping;

    private Map<String, Map<String, String>> ncCustomerCategoryMap = new HashMap<>();
    private Map<String, Map<Integer, String>> ncCustomerHospitalClassMap = new HashMap<>();
    private Map<String, Map<String, String>> ncCustomerHospitalTypeMap = new HashMap<>();


    private ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private ReentrantReadWriteLock.ReadLock rLock = rwLock.readLock();
    private ReentrantReadWriteLock.WriteLock wLock = rwLock.writeLock();

    /**
     * 热部署, 需要自行实现, zk监控客户端会在数据变更时优先调用set方法进行回调, 可在这里实现热部署
     *
     * @param ncCustomerCategoryMapping
     */
    public void setNcCustomerCategoryMapping(String ncCustomerCategoryMapping) {
        if (StringUtils.isEmpty(ncCustomerCategoryMapping)) {
            ncCustomerCategoryMapping = "{}";
        }

        try {
            wLock.lock();

            Map<String, Map<String, String>> tmpMap = JSONObject.parseObject(ncCustomerCategoryMapping, Map.class);

            ncCustomerCategoryMap.clear();
            ncCustomerCategoryMap.putAll(tmpMap);

            log.warn("nc客户分类映射更新成功,当前客户分类映射:{}", ncCustomerCategoryMap);
        } catch (Exception e) {
            log.error("nc客户分类映射更新异常", e);
            log.error("nc客户分类映射更新成功,入参:{}", ncCustomerCategoryMapping);
        } finally {
            wLock.unlock();
        }
    }

    public void setNcCustomerHospitalClassMapping(String ncCustomerHospitalClassMapping) {
        if (StringUtils.isEmpty(ncCustomerHospitalClassMapping)) {
            ncCustomerHospitalClassMapping = "{}";
        }

        try {
            wLock.lock();

            Map<String, Map<String, String>> tmpMap = JSONObject.parseObject(ncCustomerHospitalClassMapping, Map.class);

            ncCustomerHospitalClassMap.clear();
            ncCustomerHospitalClassMap.putAll(tmpMap);
            log.warn("nc客户医院等级映射更新成功,当前客户医院等级映射:{}", ncCustomerHospitalClassMap);
        } catch (Exception e) {
            log.error("nc客户医院等级映射更新异常", e);
            log.error("nc客户医院等级映射更新成功,入参:{}", ncCustomerHospitalClassMapping);
        } finally {
            wLock.unlock();
        }
    }

    public void setNcCustomerHospitalTypeMapping(String ncCustomerHospitalTypeMapping) {
        if (StringUtils.isEmpty(ncCustomerHospitalTypeMapping)) {
            ncCustomerHospitalTypeMapping = "{}";
        }

        try {
            wLock.lock();

            Map<String, Map<String, String>> tmpMap = JSONObject.parseObject(ncCustomerHospitalTypeMapping, Map.class);

            ncCustomerHospitalTypeMap.clear();
            ncCustomerHospitalTypeMap.putAll(tmpMap);
            log.warn("nc客户医院类型映射更新成功,当前客户医院类型映射:{}", ncCustomerHospitalTypeMap);
        } catch (Exception e) {
            log.error("nc客户医院类型映射更新异常", e);
            log.error("nc客户医院类型映射更新成功,入参:{}", ncCustomerHospitalTypeMapping);
        } finally {
            wLock.unlock();
        }
    }


    public Map<String, Map<String, String>> getNcCustomerCategoryMap() {
        try {
            rLock.lock();

            return ncCustomerCategoryMap;
        } finally {
            rLock.unlock();
        }
    }

    public Map<String, Map<Integer, String>> getNcCustomerHospitalClassMap() {
        try {
            rLock.lock();

            return ncCustomerHospitalClassMap;
        } finally {
            rLock.unlock();
        }
    }

    public Map<String, Map<String, String>> getNcCustomerHospitalTypeMap() {
        try {
            rLock.lock();

            return ncCustomerHospitalTypeMap;
        } finally {
            rLock.unlock();
        }
    }

}

