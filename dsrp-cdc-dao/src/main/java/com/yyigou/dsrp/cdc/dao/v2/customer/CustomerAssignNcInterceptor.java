package com.yyigou.dsrp.cdc.dao.v2.customer;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.dsrp.cdc.common.nc.NcGray;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerBase;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerChannelSyncMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@Intercepts(value = {
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
})
public class CustomerAssignNcInterceptor implements Interceptor, ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object proceed = invocation.proceed();

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        if (!mappedStatement.getResource().contains("CustomerBaseDAO")) {
            return proceed;
        }

        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        if (SqlCommandType.INSERT != sqlCommandType && SqlCommandType.UPDATE != sqlCommandType) {
            return proceed;
        }

        NcGray ncGray = applicationContext.getBean(NcGray.class);


        // insert：分派
        // update：取消分派、启停（让接口幂等去处理，这里不特殊处理）；where条件可能为customerCode和id


        CustomerBase customerBase = null;

        String enterpriseNo = null;
        String useOrgNo = null;
        String customerCode = null;

        if (SqlCommandType.INSERT == sqlCommandType) {
            customerBase = (CustomerBase) invocation.getArgs()[1];

            if (!ncGray.isEnterpriseGray(customerBase.getEnterpriseNo())) {
                return proceed;
            }
        } else if (SqlCommandType.UPDATE == sqlCommandType) {
            BoundSql boundSql = mappedStatement.getBoundSql(invocation.getArgs()[1]);
            Object parameterObject = boundSql.getParameterObject();

            if (parameterObject instanceof Map) {
                Map<?, ?> paramMap = (Map<?, ?>) parameterObject;

                // 获取UpdateWrapper
                Object updateWrapper = paramMap.get("ew");
                if (updateWrapper != null) {
                    MetaObject wrapperMeta = SystemMetaObject.forObject(updateWrapper);

                    // 获取WHERE条件SQL片段
                    String whereSql = (String) wrapperMeta.getValue("sqlSegment");

                    // 获取全部参数，后续从里面取租户编号和客户编码
                    Map<String, Object> paramNameValuePairs = (Map<String, Object>) wrapperMeta.getValue("paramNameValuePairs");


                    Pattern customerCodePattern = Pattern.compile("customer_code\\s*=\\s*#\\{ew\\.paramNameValuePairs\\.(\\w+)\\}");
                    Matcher customerCodeMatcher = customerCodePattern.matcher(whereSql);
                    if (customerCodeMatcher.find()) {
                        String customerCodeKey = customerCodeMatcher.group(1);
                        customerCode = (String) paramNameValuePairs.get(customerCodeKey);
                    }

                    if (null != customerCode) {
                        Pattern enterpriseNoPattern = Pattern.compile("enterprise_no\\s*=\\s*#\\{ew\\.paramNameValuePairs\\.(\\w+)\\}");
                        Matcher enterpriseNoMatcher = enterpriseNoPattern.matcher(whereSql);
                        if (enterpriseNoMatcher.find()) {
                            String enterpriseNoKey = enterpriseNoMatcher.group(1);
                            enterpriseNo = (String) paramNameValuePairs.get(enterpriseNoKey);

                            if (!ncGray.isEnterpriseGray(enterpriseNo)) {
                                return proceed;
                            }
                        } else {
                            log.error("没有找到enterprise_no");
                            return proceed;
                        }

                        Pattern useOrgNoPattern = Pattern.compile("use_org_no\\s*=\\s*#\\{ew\\.paramNameValuePairs\\.(\\w+)\\}");
                        Matcher useOrgNOMatcher = useOrgNoPattern.matcher(whereSql);
                        if (useOrgNOMatcher.find()) {
                            String useOrgNoKey = useOrgNOMatcher.group(1);
                            useOrgNo = (String) paramNameValuePairs.get(useOrgNoKey);
                        } else {
                            log.error("没有找到use_org_no");
                            return proceed;
                        }
                    }

                    if (null == enterpriseNo && null == customerCode && null == useOrgNo) {
                        String id = null;

                        Pattern idPattern = Pattern.compile("id\\s*=\\s*#\\{ew\\.paramNameValuePairs\\.(\\w+)\\}");
                        Matcher idMatcher = idPattern.matcher(whereSql);
                        if (idMatcher.find()) {
                            String idKey = idMatcher.group(1);
                            id = (String) paramNameValuePairs.get(idKey);
                        } else {
                            log.error("没有找到id");
                            return proceed;
                        }

                        if (null != id) {
                            CustomerBaseDAO customerBaseDAO = applicationContext.getBean(CustomerBaseDAO.class);
                            customerBase = customerBaseDAO.selectById(id);

                            if (null == customerBase) {
                                log.error("没有找到customerBase");
                                return proceed;
                            }

                            enterpriseNo = customerBase.getEnterpriseNo();
                            customerCode = customerBase.getCustomerCode();
                            useOrgNo = customerBase.getUseOrgNo();

                            if (!ncGray.isEnterpriseGray(enterpriseNo)) {
                                return proceed;
                            }
                        }
                    }
                }
            }
        }


        CustomerChannelSyncMappingDAO customerChannelSyncMappingDAO = applicationContext.getBean(CustomerChannelSyncMappingDAO.class);

        CustomerChannelSyncMapping customerChannelSyncMappingDb = customerChannelSyncMappingDAO.selectOne(Wrappers.<CustomerChannelSyncMapping>lambdaQuery()
                .eq(CustomerChannelSyncMapping::getEnterpriseNo, enterpriseNo)
                .eq(CustomerChannelSyncMapping::getOrgNo, useOrgNo)
                .eq(CustomerChannelSyncMapping::getInnerBillNo, customerCode)
                .eq(CustomerChannelSyncMapping::getChannelCode, "NC")
                .eq(CustomerChannelSyncMapping::getViewNo, "BDC_CUSTOMER_BASE"));

        if (null == customerChannelSyncMappingDb) {
            // 新增
            CustomerChannelSyncMapping customerChannelSyncMapping = new CustomerChannelSyncMapping();
            customerChannelSyncMapping.setEnterpriseNo(enterpriseNo);
            customerChannelSyncMapping.setOrgNo(useOrgNo);
            customerChannelSyncMapping.setInnerBillNo(customerCode);
            customerChannelSyncMapping.setChannelCode("NC");
            customerChannelSyncMapping.setViewNo("BDC_CUSTOMER_BASE");
            customerChannelSyncMapping.setOutBillNo("");
            customerChannelSyncMapping.setSyncStatus(0);
            customerChannelSyncMapping.setSyncResult("");
            customerChannelSyncMapping.setFailTimes(0);
            customerChannelSyncMapping.setRemark("客户档案分派变更后触发未同步");
//        customerChannelSyncMapping.setExt1("");
//        customerChannelSyncMapping.setExt2("");
//        customerChannelSyncMapping.setExt3("");
//        customerChannelSyncMapping.setExt4("");
//        customerChannelSyncMapping.setExt5(0);
//        customerChannelSyncMapping.setExt6(0);
            customerChannelSyncMapping.setCreateTime(DateUtil.getCurrentDate());
            customerChannelSyncMapping.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMappingDAO.insert(customerChannelSyncMapping);
        } else {
            // 更新
            customerChannelSyncMappingDb.setSyncStatus(0);
            customerChannelSyncMappingDb.setSyncResult("");
            customerChannelSyncMappingDb.setRemark("客户档案分派变更后触发未同步");
            customerChannelSyncMappingDb.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMappingDAO.updateById(customerChannelSyncMappingDb);
        }

        return proceed;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}