package com.yyigou.dsrp.cdc.dao.v2.customer;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yyigou.ddc.common.util.DateUtil;
import com.yyigou.dsrp.cdc.common.enums.DeletedEnum;
import com.yyigou.dsrp.cdc.common.nc.NcGray;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerChannelSyncMapping;
import com.yyigou.dsrp.cdc.dao.v2.customer.entity.CustomerV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@Intercepts(value = {
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
})
public class CustomerNcInterceptor implements Interceptor, ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object proceed = invocation.proceed();

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        if (!mappedStatement.getResource().contains("CustomerV2DAO")) {
            return proceed;
        }

        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        if (SqlCommandType.INSERT != sqlCommandType && SqlCommandType.UPDATE != sqlCommandType) {
            return proceed;
        }

        NcGray ncGray = applicationContext.getBean(NcGray.class);

        CustomerV2 customerV2 = null;
        if (SqlCommandType.INSERT == sqlCommandType) {
            customerV2 = (CustomerV2) invocation.getArgs()[1];

            if (!ncGray.isEnterpriseGray(customerV2.getEnterpriseNo())) {
                return proceed;
            }
        } else if (SqlCommandType.UPDATE == sqlCommandType) {
            BoundSql boundSql = mappedStatement.getBoundSql(invocation.getArgs()[1]);
            Object parameterObject = boundSql.getParameterObject();

            if (parameterObject instanceof Map) {
                Map<?, ?> paramMap = (Map<?, ?>) parameterObject;

                // 获取UpdateWrapper
                Object updateWrapper = paramMap.get("ew");
                if (updateWrapper != null) {
                    MetaObject wrapperMeta = SystemMetaObject.forObject(updateWrapper);

                    // 获取WHERE条件SQL片段
                    String whereSql = (String) wrapperMeta.getValue("sqlSegment");

                    // 获取全部参数，后续从里面取租户编号和客户编码
                    Map<String, Object> paramNameValuePairs = (Map<String, Object>) wrapperMeta.getValue("paramNameValuePairs");


                    String enterpriseNo = null;
                    String customerCode = null;


                    Pattern enterpriseNoPattern = Pattern.compile("enterprise_no\\s*=\\s*#\\{ew\\.paramNameValuePairs\\.(\\w+)\\}");
                    Matcher enterpriseNoMatcher = enterpriseNoPattern.matcher(whereSql);
                    if (enterpriseNoMatcher.find()) {
                        String enterpriseNoKey = enterpriseNoMatcher.group(1);
                        enterpriseNo = (String) paramNameValuePairs.get(enterpriseNoKey);

                        if (!ncGray.isEnterpriseGray(enterpriseNo)) {
                            return proceed;
                        }
                    } else {
                        log.error("没有找到enterprise_no");
                        return proceed;
                    }

                    Pattern customerCodePattern = Pattern.compile("customer_code\\s*=\\s*#\\{ew\\.paramNameValuePairs\\.(\\w+)\\}");
                    Matcher customerCodeMatcher = customerCodePattern.matcher(whereSql);
                    if (customerCodeMatcher.find()) {
                        String customerCodeKey = customerCodeMatcher.group(1);
                        customerCode = (String) paramNameValuePairs.get(customerCodeKey);
                    } else {
                        log.error("没有找到customer_code");
                        return proceed;
                    }

                    CustomerV2DAO customerV2DAO = applicationContext.getBean(CustomerV2DAO.class);
                    customerV2 = customerV2DAO.selectOne(Wrappers.<CustomerV2>lambdaQuery()
                            .eq(CustomerV2::getEnterpriseNo, enterpriseNo)
                            .eq(CustomerV2::getCustomerCode, customerCode)
                            .eq(CustomerV2::getDeleted, DeletedEnum.UN_DELETE.getValue())
                    );

                    if (null == customerV2) {
                        log.error("没有找到customer");
                        return proceed;
                    }
                }
            }
        }

        CustomerChannelSyncMappingDAO customerChannelSyncMappingDAO = applicationContext.getBean(CustomerChannelSyncMappingDAO.class);

        CustomerChannelSyncMapping customerChannelSyncMappingDb = customerChannelSyncMappingDAO.selectOne(Wrappers.<CustomerChannelSyncMapping>lambdaQuery()
                .eq(CustomerChannelSyncMapping::getEnterpriseNo, customerV2.getEnterpriseNo())
                .eq(CustomerChannelSyncMapping::getInnerBillNo, customerV2.getCustomerCode())
                .eq(CustomerChannelSyncMapping::getOrgNo, customerV2.getManageOrgNo())
                .eq(CustomerChannelSyncMapping::getChannelCode, "NC")
                .eq(CustomerChannelSyncMapping::getViewNo, "BDC_CUSTOMER"));

        if (null == customerChannelSyncMappingDb) {
            // 新增
            CustomerChannelSyncMapping customerChannelSyncMapping = new CustomerChannelSyncMapping();
            customerChannelSyncMapping.setEnterpriseNo(customerV2.getEnterpriseNo());
            customerChannelSyncMapping.setInnerBillNo(customerV2.getCustomerCode());
            customerChannelSyncMapping.setOrgNo(customerV2.getManageOrgNo());
            customerChannelSyncMapping.setChannelCode("NC");
            customerChannelSyncMapping.setViewNo("BDC_CUSTOMER");
            customerChannelSyncMapping.setOutBillNo("");
            customerChannelSyncMapping.setSyncStatus(0);
            customerChannelSyncMapping.setSyncResult("");
            customerChannelSyncMapping.setFailTimes(0);
            customerChannelSyncMapping.setRemark("客户档案变更后触发未同步");
//        customerChannelSyncMapping.setExt1("");
//        customerChannelSyncMapping.setExt2("");
//        customerChannelSyncMapping.setExt3("");
//        customerChannelSyncMapping.setExt4("");
//        customerChannelSyncMapping.setExt5(0);
//        customerChannelSyncMapping.setExt6(0);
            customerChannelSyncMapping.setCreateTime(DateUtil.getCurrentDate());
            customerChannelSyncMapping.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMappingDAO.insert(customerChannelSyncMapping);
        } else {
            // 更新
            customerChannelSyncMappingDb.setSyncStatus(0);
            customerChannelSyncMappingDb.setSyncResult("");
            customerChannelSyncMappingDb.setRemark("客户档案变更后触发未同步");
            customerChannelSyncMappingDb.setOperateTime(DateUtil.getCurrentDate());
            customerChannelSyncMappingDAO.updateById(customerChannelSyncMappingDb);
        }

        return proceed;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}