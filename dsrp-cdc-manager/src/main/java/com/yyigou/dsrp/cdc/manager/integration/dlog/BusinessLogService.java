package com.yyigou.dsrp.cdc.manager.integration.dlog;

import com.yyigou.ddc.common.message.MessageUtil;
import com.yyigou.ddc.common.service.CallResult;
import com.yyigou.ddc.common.service.ServiceBaseAbstract;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.ddc.services.dlog.api.ULogAPI;
import com.yyigou.ddc.services.dlog.component.ULogClientForBusiness;
import com.yyigou.ddc.services.dlog.component.ULogClientForChannel;
import com.yyigou.ddc.services.dlog.dto.IntegrationLogDTO;
import com.yyigou.ddc.services.dlog.dto.ulog.ULogBusinessLogDTO;
import com.yyigou.ddc.services.dlog.dto.ulog.ULogChannelLogDTO;
import com.yyigou.ddc.services.dlog.vo.IntegrationLogSaveVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class BusinessLogService {
    @Autowired
    private ULogClientForBusiness uLogClientForBusiness;

    @Resource
    private ULogClientForChannel uLogClientForChannel;

    @Resource
    private ULogAPI uLogAPI;

    public void saveLog(String logLv, String businessType, String businessKey, String businessContext, String msg, String businessValue, String businessRemark) {
        ULogBusinessLogDTO logDTO = new ULogBusinessLogDTO();
        logDTO.fromSessionUser(ServiceBaseAbstract.currentRequest().getSession());
        logDTO.setLogLevel(logLv);
        logDTO.setBusinessType(businessType); // 业务类型
        logDTO.setBusinessKey(businessKey); // 业务流水号
        logDTO.setBusinessContext(businessContext); // 业务操作说明
        logDTO.setMessage(msg); // 错误信息, 如果错误日志, 建议填写
        logDTO.setBusinessValue(businessValue); // 业务数据, 可选, 为方便分析建议填写, 这里填写入参对象
        logDTO.setBusinessRemark(businessRemark); // 业务备注, 可选, 预留给业务侧自定义
        uLogClientForBusiness.saveLog(logDTO);
    }

    public void saveLogViaBackend(SessionUser sessionUser, String logLv, String businessType, String businessKey, String businessContext, String msg, String businessValue) {
        ULogBusinessLogDTO logDTO = new ULogBusinessLogDTO();
        logDTO.fromSessionUser(sessionUser);
        logDTO.setLogLevel(logLv);
        logDTO.setBusinessType(businessType); // 业务类型
        logDTO.setBusinessKey(businessKey); // 业务流水号
        logDTO.setBusinessContext(businessContext); // 业务操作说明
        logDTO.setMessage(msg); // 错误信息, 如果错误日志, 建议填写
        logDTO.setBusinessValue(businessValue); // 业务数据, 可选, 为方便分析建议填写, 这里填写入参对象
//        logDTO.setBusinessRemark("备注信息"); // 业务备注, 可选, 预留给业务侧自定义
        uLogClientForBusiness.saveLog(logDTO);
    }


    /**
     * 保存通道日志
     *
     * @param sessionUser 会话信息
     * @param intervalTime 三方接口请求耗时
     * @param billNo 业务单据编号
     * @param result 三方接口请求结果 成功/失败
     * @param responseJson 三方请求回参
     * @param requestJson 请求接口入参
     * @param channelUrl 三方接口地址
     * @param channelDomainUrl 通道域名地址
     * @param channelCode 通道编码
     * @param channelName 通道名称
     * */
    public String saveULogForChannel(SessionUser sessionUser, Long intervalTime, String billNo, String result,
                                     String responseJson, String requestJson, String channelUrl, String channelName,
                                     String channelCode, String channelDomainUrl){
        // 创建通道日志请求DTO
        ULogChannelLogDTO logDTO = new ULogChannelLogDTO();
        // 处理调用前
        logDTO.fromSessionUser(sessionUser); // 快速从会话构建必要信息, 比如租户, 用户等
        logDTO.setChannelDomainUrl(channelDomainUrl); // 通道域名地址, 必填
        logDTO.setChannelUri(channelUrl); // 通道资源地址, 必填
        logDTO.setChannelCode(channelCode); // 通道编码, 必填, 建议UAP统一维护平台支持的通道之后使用, 减少后期治理不规范
        logDTO.setChannelName(channelName); // 通道名称, 选填, 建议UAP统一维护平台支持的通道之后使用, 减少后期治理不规范
        logDTO.setChannelType(0);//通道类型: 0-企业自用通道, 1-系统公共通道
        logDTO.setBillNo(billNo); // 业务单据, 必填
        logDTO.setRequest(requestJson);// 请求, 必填, 调用三方接口的入参body
        logDTO.setIsCharge("0"); // 通道调用是否收费, 必填 (比如调用企查查, 短信等三方接口时, 通道日志应该设置为"1")
        logDTO.setIntervalTime(intervalTime.intValue()); // 三方请求耗时, 必填
        logDTO.setResult(result); // 结果, 必填, 根据 response的code判断
        logDTO.setResponse(responseJson); // 响应, 必填, 根据三方接口的响应获取, 一般是data
        // 发送到kafka
        uLogClientForChannel.saveLog(logDTO);
        return logDTO.getId();
    }


    /**
     * 保存业务监控日志
     *
     * @param requestId 上游请求ID
     * @param isSuccess 处理结果  false:失败  success:成功
     * @param errorCode 错误编码 参照枚举：IntegrationError
     * @param errorMsg 错误信息
     * @param businessBillType 业务单据类型 参照枚举：NcBillType
     * @param billNo 业务单据编号
     * @param enterpriseNo 租户编号
     * @param orgNo 业务组织编号
     * @param orgName 业务组织名称
     * @param tradeObject 交易对象 客户、供应商名称等
     * @param requestType 请求类型  IntegrationRequestType
     * @param subjectSystem 目标系统, 外部集成系统
     * */
    public void saveULogForRpc(String requestId, Boolean isSuccess, String errorCode, String errorMsg,
                               String businessBillType, String billNo, String enterpriseNo,
                               String orgNo, String orgName, String tradeObject, String subjectSystem,
                               Integer requestType){
        IntegrationLogDTO integrationLogDTO = new IntegrationLogDTO();
        if(requestId!=null){
            integrationLogDTO.setRequestId(requestId); // 上游请求id
        }
        integrationLogDTO.setRequestType(requestType);
        integrationLogDTO.setSuccess(isSuccess); // 是否成功
        integrationLogDTO.setErrorCode(errorCode); // 错误编码
        integrationLogDTO.setErrorMsg(errorMsg); // 错误信息
        integrationLogDTO.setBusinessBillType(businessBillType);
        integrationLogDTO.setBusinessBillNo(billNo);
        integrationLogDTO.setEnterpriseNo(enterpriseNo);
        integrationLogDTO.setSubjectSystem(subjectSystem); // 对接系统
        integrationLogDTO.setBusinessOrgNo(orgNo);
        integrationLogDTO.setBusinessOrgName(orgName);
        integrationLogDTO.setTradeObject(tradeObject);
        saveBatchULogForRpc(Collections.singletonList(integrationLogDTO));
    }


    public void saveBatchULogForRpc(List<IntegrationLogDTO> params){
        ValidatorUtils.checkEmptyThrowEx(params,"日志参数不能为空");
        CallResult<List<IntegrationLogSaveVO>> callResult = uLogAPI.saveBatchForRpc(params);
        MessageUtil.ensureCallResultSuccess(callResult);
    }
}
