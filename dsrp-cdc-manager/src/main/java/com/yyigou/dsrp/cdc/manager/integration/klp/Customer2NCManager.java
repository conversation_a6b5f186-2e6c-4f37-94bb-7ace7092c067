package com.yyigou.dsrp.cdc.manager.integration.klp;

import com.alibaba.fastjson.JSON;
import com.yyigou.ddc.common.util.ValidatorUtils;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.AssignKlpCustomerDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpCustomerAssginOrgDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.PushKlpCustomerDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.res.KlpResCommonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class Customer2NCManager {
    @Resource
    private OpenKlpCommonManager openKlpCommonManager;

    private void pushCustomerValidate(PushKlpCustomerDTO pushKlpCustomerDTO) {
        // 必填项校验
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO, "入参不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getClasstype(), "客户基本分类编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getCustprop(), "客户类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getCust_grade(), "客户等级不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getOwnership(), "所有制不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getEconomicstyle(), "经济类型不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getCountry(), "国家地区不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getTaxpayerid(), "纳税人识别号不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getName(), "客户名称不能为空");
        ValidatorUtils.checkEmptyThrowEx(pushKlpCustomerDTO.getAddress(), "企业地址不能为空");

        // 范围校验
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getClasstype().length() > 20, "客户基本分类编码不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getCust_grade().length() > 20, "客户等级不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getOwnership().length() > 20, "所有制不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getEconomicstyle().length() > 20, "经济类型不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getCountry().length() > 20, "国家地区不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getAreacl().length() > 20, "地区分类不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getTaxpayerid().length() > 20, "纳税人识别号不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getCode().length() > 20, "客户编码不能大于20");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getName().length() > 200, "客户名称不能大于200");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getShortname().length() > 200, "客户简称不能大于200");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getEname().length() > 200, "英文名称不能大于200");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getEmail().length() > 30, "邮箱不能大于30");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getAddress().length() > 100, "企业地址不能大于100");
        ValidatorUtils.checkTrueThrowEx(pushKlpCustomerDTO.getRemark().length() > 100, "备注不能大于100");
        if (CollectionUtils.isNotEmpty(pushKlpCustomerDTO.getAssignOrgList())) {
            for (KlpCustomerAssginOrgDTO klpCustomerAssginOrgDTO : pushKlpCustomerDTO.getAssignOrgList()) {
                ValidatorUtils.checkTrueThrowEx(null != klpCustomerAssginOrgDTO.getOrgCode() && klpCustomerAssginOrgDTO.getOrgCode().length() > 32, "分派组织编码不能大于32");
            }
        }
    }


    /**
     * 推送客户到NC
     *
     * @param pushKlpCustomerDTO
     * @return
     */
    public KlpResCommonVO pushCustomer(PushKlpCustomerDTO pushKlpCustomerDTO) {
        pushCustomerValidate(pushKlpCustomerDTO);

        String request = JSON.toJSONString(pushKlpCustomerDTO);

        KlpResCommonVO klpResCommonVO = openKlpCommonManager.requestForKlpForNoLog(request);

        return klpResCommonVO;
    }

    private void assignCustomerValidate(AssignKlpCustomerDTO assignKlpCustomerDTO) {
        // 必填项校验
        ValidatorUtils.checkEmptyThrowEx(assignKlpCustomerDTO, "入参不能为空");
        ValidatorUtils.checkEmptyThrowEx(assignKlpCustomerDTO.getCode(), "客户编码不能为空");
        ValidatorUtils.checkEmptyThrowEx(assignKlpCustomerDTO.getAssignStatus(), "分派状态不能为空");

        // 范围校验
        if (CollectionUtils.isNotEmpty(assignKlpCustomerDTO.getAssignOrgList())) {
            for (KlpCustomerAssginOrgDTO klpCustomerAssginOrgDTO : assignKlpCustomerDTO.getAssignOrgList()) {
                ValidatorUtils.checkTrueThrowEx(null != klpCustomerAssginOrgDTO.getOrgCode() && klpCustomerAssginOrgDTO.getOrgCode().length() > 32, "分派组织编码不能大于32");
            }
        }
    }

    /**
     * 分派客户到NC
     *
     * @param assignKlpCustomerDTO
     * @return
     */
    public KlpResCommonVO assignCustomer(AssignKlpCustomerDTO assignKlpCustomerDTO) {
        assignCustomerValidate(assignKlpCustomerDTO);

        String request = JSON.toJSONString(assignKlpCustomerDTO);

        KlpResCommonVO klpResCommonVO = openKlpCommonManager.requestForKlpForNoLog(request);

        return klpResCommonVO;
    }
}