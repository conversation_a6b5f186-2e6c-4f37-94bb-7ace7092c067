package com.yyigou.dsrp.cdc.manager.integration.klp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yyigou.ddc.common.exception.BusinessException;
import com.yyigou.ddc.common.exception.PojoErrorCode;
import com.yyigou.ddc.common.service.SessionUser;
import com.yyigou.ddc.common.util.StringUtil;
import com.yyigou.ddc.common.zkmonitor.EnableZkMonitor;
import com.yyigou.ddc.services.ddc.uap.common.enums.channel.ChannelEnum;
import com.yyigou.ddc.services.dlog.enums.integration.IntegrationError;
import com.yyigou.ddc.services.dlog.enums.integration.NcBillType;
import com.yyigou.dsrp.cdc.manager.integration.dlog.BusinessLogService;
import com.yyigou.dsrp.cdc.manager.integration.klp.req.KlpTokenRequestDTO;
import com.yyigou.dsrp.cdc.manager.integration.klp.res.KlpResCommonVO;
import com.yyigou.dsrp.cdc.manager.integration.klp.res.KlpTokenVO;
import com.yyigou.dsrp.cdc.manager.integration.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class OpenKlpCommonManager {
    @EnableZkMonitor(zkProperty = "klp_token_url", zkNode = "/ddc-config/common")
    private String klp_token_url;

    @EnableZkMonitor(zkProperty = "klp_push_url", zkNode = "/ddc-config/common")
    private String klp_push_url;

    @Resource
    private BusinessLogService businessLogService;


    /**
     * 接口请求时都需要先获取token
     *
     * @return
     */
    public KlpTokenVO getToken() {
        KlpTokenRequestDTO requestDTO = new KlpTokenRequestDTO();
        String result = HttpUtil.doSimpleHttpPostForKlp(klp_token_url, JSON.toJSONString(requestDTO), "UTF-8", "application/json", "5000", null, null);
        if (StringUtil.isEmpty(result)) {
            throw new BusinessException(PojoErrorCode.POJO_NULL, "查询token接口失败，返回接口为空:" + JSON.toJSONString(result));
        }
        KlpTokenVO klpTokenVO = JSON.parseObject(result, KlpTokenVO.class);
        return klpTokenVO;
    }

    /**
     * 凯莱谱公用推送接口
     *
     * @param url
     * @param request
     * @return
     */
    public KlpResCommonVO requestForKlpForNoLog(String request) {
        KlpTokenVO klpTokenVO = this.getToken();
        if (klpTokenVO == null) {
            throw new BusinessException(PojoErrorCode.POJO_NULL, "查询token接口失败，返回接口为空:" + JSON.toJSONString(klpTokenVO));
        }
        String result = HttpUtil.doSimpleHttpPostForKlp(klp_push_url, request, "UTF-8", "application/json", "5000", null, klpTokenVO.getUap_token());
        if (StringUtil.isEmpty(result)) {
            throw new BusinessException(PojoErrorCode.POJO_NULL, "推送失败，返回接口为空:" + JSON.toJSONString(result));
        }

        KlpResCommonVO klpResCommonVO = JSON.parseObject(result, KlpResCommonVO.class);

        return klpResCommonVO;
    }

    /**
     * 凯莱谱公用推送接口
     *
     * @param request
     * @param sessionUser
     * @param billNo
     * @param orgNo
     * @param orgName
     * @param businessBillType
     *
     * @return
     */
    public Boolean requestForKlp(String request, SessionUser sessionUser, String billNo, String orgNo, String orgName, String businessBillType) {
        long startTime = System.currentTimeMillis();
        String state = null;
        String errorMsg = null;
        Boolean isSuccess = true;
        String errorCode = null;
        String result = null;
        try {
            KlpTokenVO klpTokenVO = this.getToken();
            if (klpTokenVO == null) {
                throw new BusinessException(PojoErrorCode.POJO_NULL, "查询token接口失败，返回接口为空:" + JSON.toJSONString(klpTokenVO));
            }
            result = HttpUtil.doSimpleHttpPostForKlp(klp_push_url, request, "UTF-8", "application/json", "5000", null, klpTokenVO.getUap_token());
            if (StringUtil.isEmpty(result)) {
                throw new BusinessException(PojoErrorCode.POJO_NULL, "推送失败，返回接口为空:" + JSON.toJSONString(result));
            }
            JSONObject response = JSONObject.parseObject(result);
            state = response.getString("state");
            errorMsg = response.getString("msg");
            isSuccess = true;
            errorCode = null;
            if ("1".equals(state)) {
                return true;
            } else {
                isSuccess = false;
                errorCode = IntegrationError.OUTER_INVOKE_ERR.getErrorCode();
                log.error("推送失败，错误码：" + state + "，错误信息：" + response.get("msg"));
            }
        } catch (Exception e) {
            log.error("推送失败" + e.getMessage());
        } finally {
            // 保存通道日志
            String id = businessLogService.saveULogForChannel(sessionUser, System.currentTimeMillis() - startTime, billNo, isSuccess ? "成功" : "失败", JSON.toJSONString(request)
                    , JSON.toJSONString(request), klp_push_url, ChannelEnum.NC.getValue(), ChannelEnum.NC.getName(), klp_push_url);

            // 保存业务集成日志
            businessLogService.saveULogForRpc(id, isSuccess, errorCode, errorMsg, businessBillType, billNo,
                    sessionUser.getEnterpriseNo(), orgNo, orgName, sessionUser.getEnterpriseName()
                    , NcBillType.systemCode, 1);
        }
        return true;
    }
}
