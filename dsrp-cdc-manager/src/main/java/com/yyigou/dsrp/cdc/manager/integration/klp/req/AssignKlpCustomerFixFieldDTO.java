package com.yyigou.dsrp.cdc.manager.integration.klp.req;

import lombok.Getter;

import java.io.Serializable;

@Getter
public class AssignKlpCustomerFixFieldDTO implements Serializable {
    // 来源系统*，固定值：DMS，表示来自DMS系统
    private String data_source = "DMS";

    // 接口类型*：固定值：customer_assign，表示客户分配接口
    private String c_bill_type = "customer_assign";

    // 数据类型*：固定值：2，表示客户档案
    private String custsupp = "2";

}
