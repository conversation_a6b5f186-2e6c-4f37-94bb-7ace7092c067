package com.yyigou.dsrp.cdc.manager.integration.klp.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PushKlpCustomerDTO extends PushKlpCustomerFixFieldDTO implements Serializable {
    // 客户基本分类编码*
    private String classtype;

    // 客户等级
    private String cust_grade;

    // 所有制*
    private String ownership;

    // 经济类型*
    private String economicstyle;

    // 国家地区*
    private String country;

    // 地区分类*
    private String areacl;

    // 纳税人识别码*
    private String taxpayerid;

    // 客户编码*
    private String code;

    // 客户名称*
    private String name;

    // 客户简称
    private String shortname;

    // 英文名称
    private String ename;

    // 邮箱
    private String email;

    // 企业地址*
    private String address;

    // 备注
    private String remark;
}
