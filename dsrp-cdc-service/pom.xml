<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yyigou.dsrp.cdc</groupId>
        <artifactId>service-dsrp-cdc</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dsrp-cdc-service</artifactId>
    <packaging>jar</packaging>

    <name>dsrp-cdc-service</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.dsrp.cdc</groupId>
            <artifactId>dsrp-cdc-common</artifactId>
        </dependency>
        <!--分布式事务相关依赖-->
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-distributed-transaction</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-cache-redisson3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-dubbo-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc.services</groupId>
            <artifactId>ddc-dw-doris-util-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>


        <!--  防止单测时dubbo的注解不会生效 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yyigou.ddc</groupId>
            <artifactId>common-dubbo-registry-zookeeper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dangdang</groupId>
            <artifactId>elastic-job-lite-core</artifactId>
            <version>2.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.dangdang</groupId>
            <artifactId>elastic-job-lite-spring</artifactId>
            <version>2.1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>
